import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/subscription_plan.dart';
import '../models/user_model.dart';
import '../models/device_model.dart';
import '../models/analytics_model.dart';

class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  factory FirestoreService() => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _plansCollection = 'subscription_plans';
  static const String _usersCollection = 'users';
  static const String _devicesCollection = 'devices';
  static const String _analyticsCollection = 'analytics';
  static const String _logsCollection = 'system_logs';

  // Add a new subscription plan
  Future<String> addPlan(SubscriptionPlan plan) async {
    try {
      final planData = plan.toMap();
      planData['createdAt'] = FieldValue.serverTimestamp();
      planData['updatedAt'] = FieldValue.serverTimestamp();

      final docRef =
          await _firestore.collection(_plansCollection).add(planData);
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add plan: $e');
    }
  }

  // Get all subscription plans
  Future<List<SubscriptionPlan>> getPlans() async {
    try {
      final querySnapshot = await _firestore
          .collection(_plansCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('price')
          .get();

      return querySnapshot.docs
          .map((doc) => SubscriptionPlan.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to get plans: $e');
    }
  }

  // Get plans as a stream for real-time updates
  Stream<List<SubscriptionPlan>> getPlansStream() {
    return _firestore
        .collection(_plansCollection)
        .where('isActive', isEqualTo: true)
        .orderBy('price')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SubscriptionPlan.fromMap(doc.data(), doc.id))
            .toList());
  }

  // Update an existing subscription plan
  Future<void> updatePlan(String planId, SubscriptionPlan plan) async {
    try {
      final planData = plan.toMap();
      planData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(_plansCollection)
          .doc(planId)
          .update(planData);
    } catch (e) {
      throw Exception('Failed to update plan: $e');
    }
  }

  // Delete a subscription plan (soft delete by setting isActive to false)
  Future<void> deletePlan(String planId) async {
    try {
      await _firestore.collection(_plansCollection).doc(planId).update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete plan: $e');
    }
  }

  // Get a specific plan by ID
  Future<SubscriptionPlan?> getPlanById(String planId) async {
    try {
      final doc =
          await _firestore.collection(_plansCollection).doc(planId).get();

      if (doc.exists && doc.data() != null) {
        return SubscriptionPlan.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get plan: $e');
    }
  }

  // Check if a plan name already exists
  Future<bool> planNameExists(String planName, {String? excludePlanId}) async {
    try {
      Query query = _firestore
          .collection(_plansCollection)
          .where('planName', isEqualTo: planName)
          .where('isActive', isEqualTo: true);

      final querySnapshot = await query.get();

      if (excludePlanId != null) {
        return querySnapshot.docs.any((doc) => doc.id != excludePlanId);
      }

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      throw Exception('Failed to check plan name: $e');
    }
  }

  // Get plans count for admin dashboard
  Future<int> getPlansCount() async {
    try {
      final querySnapshot = await _firestore
          .collection(_plansCollection)
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get plans count: $e');
    }
  }

  // Create default plans if none exist
  Future<void> createDefaultPlans() async {
    try {
      final existingPlans = await getPlans();
      if (existingPlans.isNotEmpty) return;

      final defaultPlans = [
        SubscriptionPlan(
          id: '',
          planName: 'Basic',
          price: 4.99,
          dailyUsage: 10.0,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: [
            '10L daily water quota',
            'Basic TDS monitoring',
            'Email support'
          ],
        ),
        SubscriptionPlan(
          id: '',
          planName: 'Premium',
          price: 9.99,
          dailyUsage: 50.0,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: [
            '50L daily water quota',
            'Advanced TDS monitoring',
            'Priority support',
            'Usage history'
          ],
        ),
        SubscriptionPlan(
          id: '',
          planName: 'Enterprise',
          price: 29.99,
          dailyUsage: double.infinity,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: [
            'Unlimited daily water quota',
            'Real-time monitoring',
            '24/7 support',
            'Advanced analytics',
            'Multiple devices'
          ],
        ),
      ];

      for (final plan in defaultPlans) {
        await addPlan(plan);
      }
    } catch (e) {
      throw Exception('Failed to create default plans: $e');
    }
  }

  // User Management Functions

  // Create a new user document in Firestore
  Future<void> createUser(UserModel user) async {
    try {
      final userData = user.toMap();
      userData['createdAt'] = FieldValue.serverTimestamp();
      userData['updatedAt'] = FieldValue.serverTimestamp();
      userData['lastLoginAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(_usersCollection).doc(user.id).set(userData);
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  // Create a new user with default subscription plan
  Future<void> createUserWithDefaults({
    required String uid,
    required String email,
    String displayName = '',
    String phoneNumber = '',
  }) async {
    try {
      // Get the default Basic plan
      final plans = await getPlans();
      final basicPlan = plans.firstWhere(
        (plan) => plan.planName.toLowerCase() == 'basic',
        orElse: () => SubscriptionPlan(
          id: '',
          planName: 'Basic',
          price: 4.99,
          dailyUsage: 10.0,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: [
            '10L daily water quota',
            'Basic TDS monitoring',
            'Email support'
          ],
        ),
      );

      final now = DateTime.now();
      final subscriptionEndDate =
          now.add(Duration(days: 30 * basicPlan.planValidityMonths));

      final user = UserModel(
        id: uid,
        email: email,
        displayName: displayName,
        phoneNumber: phoneNumber,
        isActive: true,
        isAdmin: false,
        subscriptionPlanId: basicPlan.id,
        subscriptionPlanName: basicPlan.planName,
        dailyQuota: basicPlan.dailyUsage,
        currentDailyUsage: 0.0,
        subscriptionStartDate: now,
        subscriptionEndDate: subscriptionEndDate,
        createdAt: now,
        updatedAt: now,
        lastLoginAt: now,
        deviceIds: [],
        preferences: {},
        totalUsage: 0.0,
        loginCount: 1,
      );

      await createUser(user);
    } catch (e) {
      throw Exception('Failed to create user with defaults: $e');
    }
  }

  Future<List<UserModel>> getAllUsers() async {
    try {
      final querySnapshot = await _firestore
          .collection(_usersCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to get users: $e');
    }
  }

  Stream<List<UserModel>> getUsersStream() {
    return _firestore
        .collection(_usersCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => UserModel.fromMap(doc.data(), doc.id))
            .toList());
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc =
          await _firestore.collection(_usersCollection).doc(userId).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  Future<void> updateUser(String userId, UserModel user) async {
    try {
      final userData = user.toMap();
      userData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .update(userData);
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  Future<List<UserModel>> searchUsers(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(_usersCollection)
          .where('email', isGreaterThanOrEqualTo: query)
          .where('email', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  Future<int> getUsersCount() async {
    try {
      final querySnapshot = await _firestore.collection(_usersCollection).get();
      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get users count: $e');
    }
  }

  Future<int> getActiveUsersCount() async {
    try {
      final querySnapshot = await _firestore
          .collection(_usersCollection)
          .where('isActive', isEqualTo: true)
          .get();
      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get active users count: $e');
    }
  }

  // Device Management Functions
  Future<List<DeviceModel>> getAllDevices() async {
    try {
      final querySnapshot = await _firestore
          .collection(_devicesCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => DeviceModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to get devices: $e');
    }
  }

  Stream<List<DeviceModel>> getDevicesStream() {
    return _firestore
        .collection(_devicesCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => DeviceModel.fromMap(doc.data(), doc.id))
            .toList());
  }

  Future<void> updateDevice(String deviceId, DeviceModel device) async {
    try {
      final deviceData = device.toMap();
      deviceData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(_devicesCollection)
          .doc(deviceId)
          .update(deviceData);
    } catch (e) {
      throw Exception('Failed to update device: $e');
    }
  }

  Future<void> updateDeviceStatus(
      String deviceId, bool isOnline, Map<String, dynamic>? sensorData) async {
    try {
      final updateData = {
        'isOnline': isOnline,
        'lastSeen': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (sensorData != null) {
        updateData['sensorData'] = sensorData;
        if (sensorData.containsKey('tds')) {
          updateData['tdsLevel'] = sensorData['tds'];
        }
        if (sensorData.containsKey('flow')) {
          updateData['flowRate'] = sensorData['flow'];
        }
        if (sensorData.containsKey('relay')) {
          updateData['relayState'] = sensorData['relay'] == 'ON';
        }
      }

      await _firestore
          .collection(_devicesCollection)
          .doc(deviceId)
          .update(updateData);
    } catch (e) {
      throw Exception('Failed to update device status: $e');
    }
  }

  // Analytics Functions
  Future<void> saveAnalytics(AnalyticsModel analytics) async {
    try {
      final analyticsData = analytics.toMap();
      analyticsData['createdAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(_analyticsCollection).add(analyticsData);
    } catch (e) {
      throw Exception('Failed to save analytics: $e');
    }
  }

  Future<List<AnalyticsModel>> getAnalytics({int days = 7}) async {
    try {
      final startDate = DateTime.now().subtract(Duration(days: days));
      final querySnapshot = await _firestore
          .collection(_analyticsCollection)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .orderBy('date', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => AnalyticsModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to get analytics: $e');
    }
  }

  // System Logs Functions
  Future<void> addSystemLog(SystemLogModel log) async {
    try {
      final logData = log.toMap();
      await _firestore.collection(_logsCollection).add(logData);
    } catch (e) {
      throw Exception('Failed to add system log: $e');
    }
  }

  Future<List<SystemLogModel>> getSystemLogs({int limit = 100}) async {
    try {
      final querySnapshot = await _firestore
          .collection(_logsCollection)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => SystemLogModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to get system logs: $e');
    }
  }

  Stream<List<SystemLogModel>> getSystemLogsStream({int limit = 50}) {
    return _firestore
        .collection(_logsCollection)
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SystemLogModel.fromMap(doc.data(), doc.id))
            .toList());
  }
}
