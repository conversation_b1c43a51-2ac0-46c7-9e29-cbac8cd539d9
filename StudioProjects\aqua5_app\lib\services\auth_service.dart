import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'firestore_service.dart';

/// Authentication result class to handle both success and error states
class AuthResult {
  final bool success;
  final String? errorMessage;
  final User? user;

  AuthResult({
    required this.success,
    this.errorMessage,
    this.user,
  });

  factory AuthResult.success(User user) {
    return AuthResult(
      success: true,
      user: user,
    );
  }

  factory AuthResult.error(String message) {
    return AuthResult(
      success: false,
      errorMessage: message,
    );
  }
}

/// Service class to handle all Firebase authentication operations
class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<AuthResult> signUpWithEmail(String email, String password) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      return AuthResult.success(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error(
          'An unexpected error occurred. Please try again.');
    }
  }

  // Complete signup with email, password and user document creation
  Future<AuthResult> signUpWithEmailComplete({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      // First create the Firebase Auth user
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = userCredential.user!;

      // Update the user's display name in Firebase Auth
      await user.updateDisplayName(displayName);

      // Create user document in Firestore with default subscription
      await _firestoreService.createUserWithDefaults(
        uid: user.uid,
        email: email,
        displayName: displayName,
      );

      return AuthResult.success(user);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      // If Firestore creation fails, we should clean up the Firebase Auth user
      try {
        await _auth.currentUser?.delete();
      } catch (deleteError) {
        // Log the delete error but don't change the main error message
        debugPrint('Failed to delete user after Firestore error: $deleteError');
      }
      return AuthResult.error(
          'Failed to create user account. Please try again.');
    }
  }

  // Sign in with email and password
  Future<AuthResult> signInWithEmail(String email, String password) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return AuthResult.success(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error(
          'An unexpected error occurred. Please try again.');
    }
  }

  // Sign in with phone number (send OTP) with reCAPTCHA verification
  Future<AuthResult> sendOTP(String phoneNumber,
      {BuildContext? context}) async {
    try {
      final completer = Completer<AuthResult>();

      // For web, we need to use reCAPTCHA
      // For mobile, we can use the invisible reCAPTCHA or SafetyNet
      // This implementation works for both platforms

      // We'll use a different approach for mobile and web
      // For mobile, we'll use the default Firebase implementation
      // For web, we'll need to add a reCAPTCHA widget to the UI

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final userCredential = await _auth.signInWithCredential(credential);
            completer.complete(AuthResult.success(userCredential.user!));
          } catch (e) {
            completer.complete(
                AuthResult.error('Verification failed. Please try again.'));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          completer.complete(AuthResult.error(_getFirebaseAuthErrorMessage(e)));
        },
        codeSent: (String verificationId, int? resendToken) {
          // Return the verification ID to be used with verifyOTP
          completer.complete(AuthResult(
            success: true,
            user: null,
            errorMessage:
                verificationId, // Using errorMessage to pass verificationId
          ));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
          if (!completer.isCompleted) {
            completer.complete(AuthResult.error(
                'Verification code request timed out. Please try again.'));
          }
        },
        timeout: const Duration(seconds: 60),
      );

      return await completer.future;
    } catch (e) {
      return AuthResult.error(
          'Failed to send verification code. Please try again: $e');
    }
  }

  // Send OTP without reCAPTCHA (for resending)
  Future<AuthResult> resendOTP(String phoneNumber) async {
    try {
      final completer = Completer<AuthResult>();

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final userCredential = await _auth.signInWithCredential(credential);
            completer.complete(AuthResult.success(userCredential.user!));
          } catch (e) {
            completer.complete(
                AuthResult.error('Verification failed. Please try again.'));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          completer.complete(AuthResult.error(_getFirebaseAuthErrorMessage(e)));
        },
        codeSent: (String verificationId, int? resendToken) {
          // Return the verification ID to be used with verifyOTP
          completer.complete(AuthResult(
            success: true,
            user: null,
            errorMessage:
                verificationId, // Using errorMessage to pass verificationId
          ));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
          if (!completer.isCompleted) {
            completer.complete(AuthResult.error(
                'Verification code request timed out. Please try again.'));
          }
        },
        timeout: const Duration(seconds: 60),
      );

      return await completer.future;
    } catch (e) {
      return AuthResult.error(
          'Failed to resend verification code. Please try again.');
    }
  }

  // Verify OTP
  Future<AuthResult> verifyOTP(String verificationId, String otp) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      return AuthResult.success(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error('Failed to verify code. Please try again.');
    }
  }

  // Verify OTP and create user document for phone signup
  Future<AuthResult> verifyOTPComplete({
    required String verificationId,
    required String otp,
    required String displayName,
  }) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user!;

      // Check if this is a new user (first time signing in)
      final userDoc = await _firestoreService.getUserById(user.uid);

      if (userDoc == null) {
        // This is a new user, create user document
        await user.updateDisplayName(displayName);

        await _firestoreService.createUserWithDefaults(
          uid: user.uid,
          email: user.email ?? '',
          displayName: displayName,
          phoneNumber: user.phoneNumber ?? '',
        );
      }

      return AuthResult.success(user);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error('Failed to verify code. Please try again.');
    }
  }

  // Reset password
  Future<AuthResult> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return AuthResult(
        success: true,
        errorMessage: 'Password reset email sent to $email',
      );
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error(
          'Failed to send password reset email. Please try again.');
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _auth.signOut();
  }

  // Update user profile
  Future<AuthResult> updateProfile(
      {String? displayName, String? photoURL}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return AuthResult.error('No user is currently signed in.');
      }

      await user.updateDisplayName(displayName);
      await user.updatePhotoURL(photoURL);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.error('Failed to update profile. Please try again.');
    }
  }

  // Helper method to get user-friendly error messages
  String _getFirebaseAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'This email is already registered.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'weak-password':
        return 'Password is too weak. Please use a stronger password.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'invalid-verification-code':
        return 'The verification code is invalid. Please try again.';
      case 'invalid-verification-id':
        return 'The verification ID is invalid. Please request a new code.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection and try again.';
      default:
        return e.message ?? 'An error occurred. Please try again.';
    }
  }
}
