import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String email;
  final String displayName;
  final String phoneNumber;
  final bool isActive;
  final bool isAdmin;
  final String subscriptionPlanId;
  final String subscriptionPlanName;
  final double dailyQuota;
  final double currentDailyUsage;
  final DateTime subscriptionStartDate;
  final DateTime subscriptionEndDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastLoginAt;
  final List<String> deviceIds;
  final Map<String, dynamic> preferences;
  final double totalUsage;
  final int loginCount;

  UserModel({
    required this.id,
    required this.email,
    this.displayName = '',
    this.phoneNumber = '',
    this.isActive = true,
    this.isAdmin = false,
    this.subscriptionPlanId = '',
    this.subscriptionPlanName = 'Basic',
    this.dailyQuota = 10.0,
    this.currentDailyUsage = 0.0,
    required this.subscriptionStartDate,
    required this.subscriptionEndDate,
    required this.createdAt,
    required this.updatedAt,
    required this.lastLoginAt,
    this.deviceIds = const [],
    this.preferences = const {},
    this.totalUsage = 0.0,
    this.loginCount = 0,
  });

  factory UserModel.fromMap(Map<String, dynamic> map, String documentId) {
    return UserModel(
      id: documentId,
      email: map['email'] ?? '',
      displayName: map['displayName'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      isActive: map['isActive'] ?? true,
      isAdmin: map['isAdmin'] ?? false,
      subscriptionPlanId: map['subscriptionPlanId'] ?? '',
      subscriptionPlanName: map['subscriptionPlanName'] ?? 'Basic',
      dailyQuota: (map['dailyQuota'] ?? 10.0).toDouble(),
      currentDailyUsage: (map['currentDailyUsage'] ?? 0.0).toDouble(),
      subscriptionStartDate:
          map['subscriptionStartDate']?.toDate() ?? DateTime.now(),
      subscriptionEndDate: map['subscriptionEndDate']?.toDate() ??
          DateTime.now().add(const Duration(days: 30)),
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt']?.toDate() ?? DateTime.now(),
      lastLoginAt: map['lastLoginAt']?.toDate() ?? DateTime.now(),
      deviceIds: List<String>.from(map['deviceIds'] ?? []),
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
      totalUsage: (map['totalUsage'] ?? 0.0).toDouble(),
      loginCount: map['loginCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'displayName': displayName,
      'phoneNumber': phoneNumber,
      'isActive': isActive,
      'isAdmin': isAdmin,
      'subscriptionPlanId': subscriptionPlanId,
      'subscriptionPlanName': subscriptionPlanName,
      'dailyQuota': dailyQuota,
      'currentDailyUsage': currentDailyUsage,
      'subscriptionStartDate': Timestamp.fromDate(subscriptionStartDate),
      'subscriptionEndDate': Timestamp.fromDate(subscriptionEndDate),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'lastLoginAt': Timestamp.fromDate(lastLoginAt),
      'deviceIds': deviceIds,
      'preferences': preferences,
      'totalUsage': totalUsage,
      'loginCount': loginCount,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? phoneNumber,
    bool? isActive,
    bool? isAdmin,
    String? subscriptionPlanId,
    String? subscriptionPlanName,
    double? dailyQuota,
    double? currentDailyUsage,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    List<String>? deviceIds,
    Map<String, dynamic>? preferences,
    double? totalUsage,
    int? loginCount,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isActive: isActive ?? this.isActive,
      isAdmin: isAdmin ?? this.isAdmin,
      subscriptionPlanId: subscriptionPlanId ?? this.subscriptionPlanId,
      subscriptionPlanName: subscriptionPlanName ?? this.subscriptionPlanName,
      dailyQuota: dailyQuota ?? this.dailyQuota,
      currentDailyUsage: currentDailyUsage ?? this.currentDailyUsage,
      subscriptionStartDate:
          subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      deviceIds: deviceIds ?? this.deviceIds,
      preferences: preferences ?? this.preferences,
      totalUsage: totalUsage ?? this.totalUsage,
      loginCount: loginCount ?? this.loginCount,
    );
  }

  String get statusText => isActive ? 'Active' : 'Inactive';
  String get roleText => isAdmin ? 'Admin' : 'User';
  String get dailyUsageText =>
      '${currentDailyUsage.toStringAsFixed(1)}L / ${dailyQuota == double.infinity ? 'Unlimited' : '${dailyQuota.toStringAsFixed(0)}L'}';
  String get totalUsageText => '${totalUsage.toStringAsFixed(1)}L';
  String get deviceCountText =>
      '${deviceIds.length} device${deviceIds.length != 1 ? 's' : ''}';

  bool get isSubscriptionActive => DateTime.now().isBefore(subscriptionEndDate);
  bool get isSubscriptionExpiringSoon =>
      subscriptionEndDate.difference(DateTime.now()).inDays <= 7;

  double get dailyUsagePercentage {
    if (dailyQuota == double.infinity) return 0.0;
    return (currentDailyUsage / dailyQuota).clamp(0.0, 1.0);
  }

  bool get isDailyQuotaExceeded =>
      dailyQuota != double.infinity && currentDailyUsage >= dailyQuota;

  int get daysUntilExpiry =>
      subscriptionEndDate.difference(DateTime.now()).inDays;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, subscriptionPlan: $subscriptionPlanName, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
